<template>
  <div class="data-source-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">数据源管理</h1>
        <p class="page-description">配置和管理所有外部数据的来源，支持监控目录、API接口和数据库直连</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDrawer = true">
          <el-icon><Plus /></el-icon>
          新建数据源
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选器 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="数据源名称">
          <el-input
            v-model="filterForm.name"
            placeholder="按名称模糊搜索"
            clearable
            style="width: 200px"
            @input="handleSearch"
          />
        </el-form-item>
        <el-form-item label="数据源类型">
          <el-select
            v-model="filterForm.type"
            placeholder="请选择类型"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option label="监控目录" value="directory" />
            <el-option label="API接口" value="api" />
            <el-option label="数据库直连" value="database" />
          </el-select>
        </el-form-item>
        <el-form-item label="连接状态">
          <el-select
            v-model="filterForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 120px"
            @change="handleSearch"
          >
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="error" />
            <el-option label="未测试" value="untested" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据源列表 -->
    <div class="table-section">
      <el-table
        :data="filteredDataSources"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="数据源名称" min-width="200">
          <template #default="{ row }">
            <div class="name-cell">
              <el-icon class="type-icon">
                <component :is="getTypeIcon(row.type)" />
              </el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="数据源类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="连接状态" width="120">
          <template #default="{ row }">
            <el-badge
              :value="getStatusLabel(row.status)"
              :type="getStatusBadgeType(row.status)"
              class="status-badge"
            >
              <el-icon :class="getStatusIconClass(row.status)">
                <component :is="getStatusIcon(row.status)" />
              </el-icon>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editDataSource(row)">
              编辑
            </el-button>
            <el-button
              size="small"
              type="warning"
              @click="testConnection(row)"
              :loading="row.testing"
            >
              测试连接
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteDataSource(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新建/编辑数据源抽屉 -->
    <DataSourceDrawer
      v-model:visible="showCreateDrawer"
      :data-source="currentDataSource"
      @success="handleDrawerSuccess"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Plus,
  Folder,
  Link,
  Database,
  CircleCheck,
  CircleClose,
  Warning,
} from '@element-plus/icons-vue';
import DataSourceDrawer from './DataSourceDrawer.vue';

export default {
  name: 'DataSourceManagement',
  components: {
    DataSourceDrawer,
  },
  setup() {
    // 响应式数据
    const loading = ref(false);
    const showCreateDrawer = ref(false);
    const currentDataSource = ref(null);

    // 筛选表单
    const filterForm = reactive({
      name: '',
      type: '',
      status: '',
    });

    // 分页数据
    const pagination = reactive({
      currentPage: 1,
      pageSize: 20,
      total: 0,
    });

    // 模拟数据源列表
    const dataSources = ref([
      {
        id: 1,
        name: 'CRM系统录音数据',
        type: 'directory',
        status: 'normal',
        creator: '张三',
        createTime: '2024-01-15 10:30:00',
        testing: false,
      },
      {
        id: 2,
        name: '客服系统API接口',
        type: 'api',
        status: 'error',
        creator: '李四',
        createTime: '2024-01-14 14:20:00',
        testing: false,
      },
      {
        id: 3,
        name: '业务数据库',
        type: 'database',
        status: 'untested',
        creator: '王五',
        createTime: '2024-01-13 09:15:00',
        testing: false,
      },
    ]);

    // 计算属性 - 过滤后的数据源
    const filteredDataSources = computed(() => {
      let result = dataSources.value;

      if (filterForm.name) {
        result = result.filter(item =>
          item.name.toLowerCase().includes(filterForm.name.toLowerCase())
        );
      }

      if (filterForm.type) {
        result = result.filter(item => item.type === filterForm.type);
      }

      if (filterForm.status) {
        result = result.filter(item => item.status === filterForm.status);
      }

      pagination.total = result.length;
      const start = (pagination.currentPage - 1) * pagination.pageSize;
      const end = start + pagination.pageSize;
      return result.slice(start, end);
    });

    // 方法
    const getTypeIcon = (type) => {
      const iconMap = {
        directory: Folder,
        api: Link,
        database: Database,
      };
      return iconMap[type] || Folder;
    };

    const getTypeLabel = (type) => {
      const labelMap = {
        directory: '监控目录',
        api: 'API接口',
        database: '数据库直连',
      };
      return labelMap[type] || '未知';
    };

    const getTypeTagType = (type) => {
      const typeMap = {
        directory: 'primary',
        api: 'success',
        database: 'warning',
      };
      return typeMap[type] || '';
    };

    const getStatusIcon = (status) => {
      const iconMap = {
        normal: CircleCheck,
        error: CircleClose,
        untested: Warning,
      };
      return iconMap[status] || Warning;
    };

    const getStatusLabel = (status) => {
      const labelMap = {
        normal: '正常',
        error: '异常',
        untested: '未测试',
      };
      return labelMap[status] || '未知';
    };

    const getStatusBadgeType = (status) => {
      const typeMap = {
        normal: 'success',
        error: 'danger',
        untested: 'warning',
      };
      return typeMap[status] || 'info';
    };

    const getStatusIconClass = (status) => {
      const classMap = {
        normal: 'status-icon success',
        error: 'status-icon error',
        untested: 'status-icon warning',
      };
      return classMap[status] || 'status-icon';
    };

    const formatDateTime = (dateTime) => {
      return dateTime || '--';
    };

    const handleSearch = () => {
      pagination.currentPage = 1;
    };

    const resetFilter = () => {
      filterForm.name = '';
      filterForm.type = '';
      filterForm.status = '';
      pagination.currentPage = 1;
    };

    const handleSizeChange = (size) => {
      pagination.pageSize = size;
      pagination.currentPage = 1;
    };

    const handleCurrentChange = (page) => {
      pagination.currentPage = page;
    };

    const editDataSource = (row) => {
      currentDataSource.value = { ...row };
      showCreateDrawer.value = true;
    };

    const testConnection = async (row) => {
      row.testing = true;
      try {
        // 模拟测试连接
        await new Promise(resolve => setTimeout(resolve, 2000));
        row.status = 'normal';
        ElMessage.success('连接测试成功');
      } catch (error) {
        row.status = 'error';
        ElMessage.error('连接测试失败');
      } finally {
        row.testing = false;
      }
    };

    const deleteDataSource = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除数据源"${row.name}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        // 模拟删除操作
        const index = dataSources.value.findIndex(item => item.id === row.id);
        if (index > -1) {
          dataSources.value.splice(index, 1);
          ElMessage.success('删除成功');
        }
      } catch {
        // 用户取消删除
      }
    };

    const handleDrawerSuccess = (data) => {
      if (currentDataSource.value) {
        // 编辑模式
        const index = dataSources.value.findIndex(
          item => item.id === currentDataSource.value.id
        );
        if (index > -1) {
          dataSources.value[index] = { ...data };
        }
      } else {
        // 新建模式
        dataSources.value.unshift({
          ...data,
          id: Date.now(),
          createTime: new Date().toLocaleString(),
        });
      }
      showCreateDrawer.value = false;
      currentDataSource.value = null;
    };

    onMounted(() => {
      // 初始化数据
      pagination.total = dataSources.value.length;
    });

    return {
      loading,
      showCreateDrawer,
      currentDataSource,
      filterForm,
      pagination,
      dataSources,
      filteredDataSources,
      getTypeIcon,
      getTypeLabel,
      getTypeTagType,
      getStatusIcon,
      getStatusLabel,
      getStatusBadgeType,
      getStatusIconClass,
      formatDateTime,
      handleSearch,
      resetFilter,
      handleSizeChange,
      handleCurrentChange,
      editDataSource,
      testConnection,
      deleteDataSource,
      handleDrawerSuccess,
      // Icons
      Plus,
      Folder,
      Link,
      Database,
      CircleCheck,
      CircleClose,
      Warning,
    };
  },
};
</script>

<style scoped>
.data-source-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  margin-left: 24px;
}

.filter-section {
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-section {
  margin-top: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: right;
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-icon {
  color: #409eff;
}

.status-badge {
  display: inline-flex;
  align-items: center;
}

.status-icon {
  font-size: 16px;
}

.status-icon.success {
  color: #67c23a;
}

.status-icon.error {
  color: #f56c6c;
}

.status-icon.warning {
  color: #e6a23c;
}

/* 暗色主题适配 */
.dark .data-source-management {
  background: #141414;
}

.dark .page-header,
.dark .filter-section,
.dark .table-section,
.dark .pagination-section {
  background: #1f1f1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark .page-title {
  color: #e5eaf3;
}

.dark .page-description {
  color: #a3a6ad;
}
</style>
