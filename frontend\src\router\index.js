import { createRouter, createWebHistory } from 'vue-router';
import HomePage from '../components/HomePage.vue';
import DataSourceManagement from '../components/DataSourceManagement.vue';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomePage,
  },
  {
    path: '/0',
    name: 'HomePage',
    component: HomePage,
  },
  {
    path: '/5-1',
    name: 'DataSourceManagement',
    component: DataSourceManagement,
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router; 