<template>
  <el-drawer
    v-model="drawerVisible"
    :title="isEdit ? '编辑数据源' : '新建数据源'"
    size="600px"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center class="steps-container">
        <el-step title="选择类型" />
        <el-step title="连接配置" />
        <el-step title="字段映射" />
      </el-steps>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 第一步：选择数据源类型 -->
        <div v-if="currentStep === 0" class="step-one">
          <h3 class="step-title">选择数据源类型</h3>
          <div class="type-cards">
            <div
              v-for="type in dataSourceTypes"
              :key="type.value"
              :class="['type-card', { active: formData.type === type.value }]"
              @click="selectType(type.value)"
            >
              <div class="card-icon">
                <el-icon size="32">
                  <component :is="type.icon" />
                </el-icon>
              </div>
              <div class="card-title">{{ type.label }}</div>
              <div class="card-description">{{ type.description }}</div>
            </div>
          </div>
        </div>

        <!-- 第二步：连接配置 -->
        <div v-if="currentStep === 1" class="step-two">
          <h3 class="step-title">连接配置</h3>
          <el-form
            ref="configFormRef"
            :model="formData"
            :rules="configRules"
            label-width="120px"
            class="config-form"
          >
            <!-- 通用字段 -->
            <el-form-item label="数据源名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入数据源名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>

            <!-- 监控目录配置 -->
            <template v-if="formData.type === 'directory'">
              <el-form-item label="连接协议" prop="protocol">
                <el-select v-model="formData.protocol" placeholder="请选择协议">
                  <el-option label="SFTP" value="sftp" />
                  <el-option label="FTP" value="ftp" />
                  <el-option label="SMB" value="smb" />
                </el-select>
              </el-form-item>
              <el-form-item label="服务器地址" prop="host">
                <el-input v-model="formData.host" placeholder="请输入服务器地址" />
              </el-form-item>
              <el-form-item label="端口" prop="port">
                <el-input-number
                  v-model="formData.port"
                  :min="1"
                  :max="65535"
                  placeholder="端口号"
                />
              </el-form-item>
              <el-form-item label="认证方式" prop="authType">
                <el-radio-group v-model="formData.authType">
                  <el-radio value="password">用户名/密码</el-radio>
                  <el-radio value="key">密钥文件</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-if="formData.authType === 'password'"
                label="用户名"
                prop="username"
              >
                <el-input v-model="formData.username" placeholder="请输入用户名" />
              </el-form-item>
              <el-form-item
                v-if="formData.authType === 'password'"
                label="密码"
                prop="password"
              >
                <el-input
                  v-model="formData.password"
                  type="password"
                  placeholder="请输入密码"
                  show-password
                />
              </el-form-item>
              <el-form-item
                v-if="formData.authType === 'key'"
                label="密钥文件"
                prop="keyFile"
              >
                <el-input
                  v-model="formData.keyFile"
                  placeholder="请输入密钥文件路径"
                />
              </el-form-item>
              <el-form-item label="录音文件路径" prop="audioPath">
                <el-input
                  v-model="formData.audioPath"
                  placeholder="请输入录音文件目录路径"
                />
              </el-form-item>
              <el-form-item label="元数据文件路径" prop="metaPath">
                <el-input
                  v-model="formData.metaPath"
                  placeholder="请输入元数据文件目录路径"
                />
              </el-form-item>
            </template>

            <!-- API接口配置 -->
            <template v-if="formData.type === 'api'">
              <el-form-item label="基础URL" prop="baseUrl">
                <el-input
                  v-model="formData.baseUrl"
                  placeholder="请输入API服务的根地址"
                />
              </el-form-item>
              <el-form-item label="认证方式" prop="authType">
                <el-select v-model="formData.authType" placeholder="请选择认证方式">
                  <el-option label="无认证" value="none" />
                  <el-option label="API Key" value="apikey" />
                  <el-option label="Bearer Token" value="bearer" />
                  <el-option label="OAuth2.0" value="oauth2" />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="formData.authType === 'apikey'"
                label="API Key"
                prop="apiKey"
              >
                <el-input
                  v-model="formData.apiKey"
                  type="password"
                  placeholder="请输入API Key"
                  show-password
                />
              </el-form-item>
              <el-form-item
                v-if="formData.authType === 'bearer'"
                label="Bearer Token"
                prop="bearerToken"
              >
                <el-input
                  v-model="formData.bearerToken"
                  type="password"
                  placeholder="请输入Bearer Token"
                  show-password
                />
              </el-form-item>
              <el-form-item label="录音文件处理" prop="audioStrategy">
                <el-radio-group v-model="formData.audioStrategy">
                  <el-radio value="download">同步拉取并存储</el-radio>
                  <el-radio value="reference">引用外部链接</el-radio>
                </el-radio-group>
              </el-form-item>
            </template>

            <!-- 数据库直连配置 -->
            <template v-if="formData.type === 'database'">
              <el-form-item label="数据库类型" prop="dbType">
                <el-select v-model="formData.dbType" placeholder="请选择数据库类型">
                  <el-option label="MySQL" value="mysql" />
                  <el-option label="PostgreSQL" value="postgresql" />
                  <el-option label="SQL Server" value="sqlserver" />
                  <el-option label="Oracle" value="oracle" />
                </el-select>
              </el-form-item>
              <el-form-item label="服务器地址" prop="host">
                <el-input v-model="formData.host" placeholder="请输入数据库服务器地址" />
              </el-form-item>
              <el-form-item label="端口" prop="port">
                <el-input-number
                  v-model="formData.port"
                  :min="1"
                  :max="65535"
                  placeholder="端口号"
                />
              </el-form-item>
              <el-form-item label="数据库名" prop="database">
                <el-input v-model="formData.database" placeholder="请输入数据库名" />
              </el-form-item>
              <el-form-item label="用户名" prop="username">
                <el-input v-model="formData.username" placeholder="请输入用户名" />
              </el-form-item>
              <el-form-item label="密码" prop="password">
                <el-input
                  v-model="formData.password"
                  type="password"
                  placeholder="请输入密码"
                  show-password
                />
              </el-form-item>
              <el-form-item label="SQL查询模板" prop="sqlTemplate">
                <el-input
                  v-model="formData.sqlTemplate"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入SQL查询模板，支持变量如 {{last_execution_time}}"
                />
              </el-form-item>
            </template>
          </el-form>
        </div>

        <!-- 第三步：字段映射 -->
        <div v-if="currentStep === 2" class="step-three">
          <h3 class="step-title">字段映射配置</h3>
          <div class="mapping-description">
            将外部数据源中的字段映射到系统内部的标准字段上
          </div>
          <el-table :data="fieldMappings" class="mapping-table">
            <el-table-column label="系统标准字段" width="200">
              <template #default="{ row }">
                <div class="field-info">
                  <div class="field-name">{{ row.systemField }}</div>
                  <div class="field-desc">{{ row.description }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="外部字段路径/名称">
              <template #default="{ row, $index }">
                <el-input
                  v-model="row.externalField"
                  placeholder="如: data.user.id 或 user_id"
                  @input="updateMapping($index, row.externalField)"
                />
              </template>
            </el-table-column>
            <el-table-column label="示例值" width="150">
              <template #default="{ row }">
                <span class="example-value">{{ row.exampleValue || '--' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="drawer-footer">
        <div class="footer-left">
          <el-button
            v-if="currentStep === 1 || currentStep === 2"
            @click="testConnection"
            :loading="testing"
            type="warning"
          >
            测试连接
          </el-button>
        </div>
        <div class="footer-right">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            v-if="currentStep > 0"
            @click="prevStep"
          >
            上一步
          </el-button>
          <el-button
            v-if="currentStep < 2"
            type="primary"
            @click="nextStep"
            :disabled="!canNextStep"
          >
            下一步
          </el-button>
          <el-button
            v-if="currentStep === 2"
            type="primary"
            @click="handleSubmit"
            :loading="submitting"
          >
            {{ isEdit ? '保存' : '创建' }}
          </el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Folder, Link, Database } from '@element-plus/icons-vue';

export default {
  name: 'DataSourceDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dataSource: {
      type: Object,
      default: null,
    },
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const drawerVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value),
    });

    const isEdit = computed(() => !!props.dataSource);
    const currentStep = ref(0);
    const testing = ref(false);
    const submitting = ref(false);
    const configFormRef = ref(null);

    // 数据源类型配置
    const dataSourceTypes = [
      {
        value: 'directory',
        label: '监控目录',
        description: '通过SFTP/FTP等协议接入录音和元数据文件',
        icon: Folder,
      },
      {
        value: 'api',
        label: 'API接口',
        description: '通过HTTP/S接口从外部系统拉取数据',
        icon: Link,
      },
      {
        value: 'database',
        label: '数据库直连',
        description: '直接连接到业务数据库进行只读查询',
        icon: Database,
      },
    ];

    // 表单数据
    const formData = reactive({
      name: '',
      type: '',
      // 通用字段
      host: '',
      port: null,
      username: '',
      password: '',
      // 监控目录特有
      protocol: 'sftp',
      authType: 'password',
      keyFile: '',
      audioPath: '',
      metaPath: '',
      // API接口特有
      baseUrl: '',
      apiKey: '',
      bearerToken: '',
      audioStrategy: 'download',
      // 数据库特有
      dbType: 'mysql',
      database: '',
      sqlTemplate: '',
    });

    // 字段映射配置
    const fieldMappings = ref([
      {
        systemField: '录音唯一ID',
        description: '通话录音的唯一标识符',
        externalField: '',
        exampleValue: '',
      },
      {
        systemField: '通话开始时间',
        description: '通话开始的时间戳',
        externalField: '',
        exampleValue: '',
      },
      {
        systemField: '坐席工号',
        description: '客服坐席的工号或ID',
        externalField: '',
        exampleValue: '',
      },
      {
        systemField: '客户号码',
        description: '客户的电话号码',
        externalField: '',
        exampleValue: '',
      },
      {
        systemField: '录音文件路径',
        description: '录音文件的存储路径或URL',
        externalField: '',
        exampleValue: '',
      },
    ]);

    // 表单验证规则
    const configRules = computed(() => {
      const baseRules = {
        name: [
          { required: true, message: '请输入数据源名称', trigger: 'blur' },
        ],
      };

      if (formData.type === 'directory') {
        return {
          ...baseRules,
          protocol: [
            { required: true, message: '请选择连接协议', trigger: 'change' },
          ],
          host: [
            { required: true, message: '请输入服务器地址', trigger: 'blur' },
          ],
          port: [
            { required: true, message: '请输入端口号', trigger: 'blur' },
          ],
          username: [
            { required: true, message: '请输入用户名', trigger: 'blur' },
          ],
          password: [
            { required: true, message: '请输入密码', trigger: 'blur' },
          ],
          audioPath: [
            { required: true, message: '请输入录音文件路径', trigger: 'blur' },
          ],
          metaPath: [
            { required: true, message: '请输入元数据文件路径', trigger: 'blur' },
          ],
        };
      } else if (formData.type === 'api') {
        return {
          ...baseRules,
          baseUrl: [
            { required: true, message: '请输入基础URL', trigger: 'blur' },
          ],
          authType: [
            { required: true, message: '请选择认证方式', trigger: 'change' },
          ],
        };
      } else if (formData.type === 'database') {
        return {
          ...baseRules,
          dbType: [
            { required: true, message: '请选择数据库类型', trigger: 'change' },
          ],
          host: [
            { required: true, message: '请输入服务器地址', trigger: 'blur' },
          ],
          port: [
            { required: true, message: '请输入端口号', trigger: 'blur' },
          ],
          database: [
            { required: true, message: '请输入数据库名', trigger: 'blur' },
          ],
          username: [
            { required: true, message: '请输入用户名', trigger: 'blur' },
          ],
          password: [
            { required: true, message: '请输入密码', trigger: 'blur' },
          ],
          sqlTemplate: [
            { required: true, message: '请输入SQL查询模板', trigger: 'blur' },
          ],
        };
      }

      return baseRules;
    });

    // 计算是否可以进入下一步
    const canNextStep = computed(() => {
      if (currentStep.value === 0) {
        return !!formData.type;
      }
      return true;
    });

    // 方法
    const selectType = (type) => {
      formData.type = type;
    };

    const nextStep = async () => {
      if (currentStep.value === 1) {
        // 验证配置表单
        try {
          await configFormRef.value.validate();
          currentStep.value++;
        } catch (error) {
          ElMessage.error('请完善配置信息');
        }
      } else {
        currentStep.value++;
      }
    };

    const prevStep = () => {
      if (currentStep.value > 0) {
        currentStep.value--;
      }
    };

    const testConnection = async () => {
      testing.value = true;
      try {
        // 模拟测试连接
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 模拟获取示例数据并更新字段映射
        fieldMappings.value.forEach((mapping, index) => {
          if (mapping.externalField) {
            mapping.exampleValue = `示例值${index + 1}`;
          }
        });
        
        ElMessage.success('连接测试成功');
      } catch (error) {
        ElMessage.error('连接测试失败');
      } finally {
        testing.value = false;
      }
    };

    const updateMapping = (index, value) => {
      fieldMappings.value[index].externalField = value;
    };

    const handleSubmit = async () => {
      submitting.value = true;
      try {
        // 模拟提交
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const submitData = {
          ...formData,
          fieldMappings: fieldMappings.value,
          status: 'untested',
          creator: '当前用户',
        };
        
        emit('success', submitData);
        ElMessage.success(isEdit.value ? '编辑成功' : '创建成功');
        resetForm();
      } catch (error) {
        ElMessage.error('操作失败');
      } finally {
        submitting.value = false;
      }
    };

    const handleClose = () => {
      drawerVisible.value = false;
      resetForm();
    };

    const resetForm = () => {
      currentStep.value = 0;
      Object.keys(formData).forEach(key => {
        if (typeof formData[key] === 'string') {
          formData[key] = '';
        } else if (typeof formData[key] === 'number') {
          formData[key] = null;
        }
      });
      formData.protocol = 'sftp';
      formData.authType = 'password';
      formData.audioStrategy = 'download';
      formData.dbType = 'mysql';
      
      fieldMappings.value.forEach(mapping => {
        mapping.externalField = '';
        mapping.exampleValue = '';
      });
    };

    // 监听数据源变化，用于编辑模式
    watch(
      () => props.dataSource,
      (newVal) => {
        if (newVal) {
          Object.assign(formData, newVal);
          if (newVal.fieldMappings) {
            fieldMappings.value = [...newVal.fieldMappings];
          }
        }
      },
      { immediate: true }
    );

    return {
      drawerVisible,
      isEdit,
      currentStep,
      testing,
      submitting,
      configFormRef,
      dataSourceTypes,
      formData,
      fieldMappings,
      configRules,
      canNextStep,
      selectType,
      nextStep,
      prevStep,
      testConnection,
      updateMapping,
      handleSubmit,
      handleClose,
      // Icons
      Folder,
      Link,
      Database,
    };
  },
};
</script>

<style scoped>
.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.steps-container {
  margin-bottom: 32px;
}

.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 8px;
}

.step-title {
  margin: 0 0 24px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 第一步样式 */
.type-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.type-card {
  padding: 24px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.type-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
}

.type-card.active {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.card-icon {
  margin-bottom: 16px;
  color: #409eff;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
}

.card-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

/* 第二步样式 */
.config-form {
  max-width: 100%;
}

/* 第三步样式 */
.mapping-description {
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

.mapping-table {
  width: 100%;
}

.field-info {
  line-height: 1.4;
}

.field-name {
  font-weight: 600;
  color: #303133;
}

.field-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.example-value {
  font-size: 12px;
  color: #67c23a;
  font-family: monospace;
}

/* 底部操作栏 */
.drawer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0 0 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 24px;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 12px;
}

/* 暗色主题适配 */
.dark .step-title {
  color: #e5eaf3;
}

.dark .type-card {
  border-color: #4c4d4f;
  background: #1f1f1f;
}

.dark .type-card:hover {
  border-color: #409eff;
}

.dark .type-card.active {
  background-color: #1a2332;
}

.dark .card-title {
  color: #e5eaf3;
}

.dark .card-description {
  color: #a3a6ad;
}

.dark .mapping-description {
  background: #262727;
  color: #a3a6ad;
}

.dark .field-name {
  color: #e5eaf3;
}

.dark .field-desc {
  color: #73767a;
}

.dark .drawer-footer {
  border-top-color: #4c4d4f;
}
</style>
